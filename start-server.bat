@echo off
echo.
echo 🚀 Starting CMS Logistics Website Local Server...
echo 📁 Project Directory: %CD%
echo.

REM Check if Python 3 is available
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Python found - Starting HTTP server on port 8000
    echo 🌐 Website will be available at: http://localhost:8000
    echo.
    echo 📝 Press Ctrl+C to stop the server
    echo ----------------------------------------
    python -m http.server 8000
    goto :end
)

REM Check if PHP is available
php --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ PHP found - Starting HTTP server on port 8000
    echo 🌐 Website will be available at: http://localhost:8000
    echo.
    echo 📝 Press Ctrl+C to stop the server
    echo ----------------------------------------
    php -S localhost:8000
    goto :end
)

REM Check if Node.js is available
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Node.js found - Attempting to use http-server
    echo 🌐 Website will be available at: http://localhost:8000
    echo.
    echo 📝 Press Ctrl+C to stop the server
    echo ----------------------------------------
    npx http-server -p 8000
    goto :end
)

echo ❌ No suitable web server found!
echo.
echo Please install one of the following:
echo   - Python 3: https://www.python.org/downloads/
echo   - PHP: https://www.php.net/downloads
echo   - Node.js: https://nodejs.org/en/download/
echo.
echo Or simply open index.html directly in your web browser.
pause

:end
