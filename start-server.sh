#!/bin/bash

# CMS Logistics Website - Local Development Server
# This script starts a local web server for development and testing

echo "🚀 Starting CMS Logistics Website Local Server..."
echo "📁 Project Directory: $(pwd)"
echo ""

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 1  # Port is in use
    else
        return 0  # Port is available
    fi
}

# Find available port starting from 8000
find_available_port() {
    local port=8000
    while [ $port -le 8010 ]; do
        if check_port $port; then
            echo $port
            return
        fi
        ((port++))
    done
    echo "8000"  # fallback
}

# Check if Python 3 is available
if command -v python3 &> /dev/null; then
    PORT=$(find_available_port)
    if [ $PORT -ne 8000 ]; then
        echo "⚠️  Port 8000 is in use, using port $PORT instead"
    fi
    echo "✅ Python 3 found - Starting HTTP server on port $PORT"
    echo "🌐 Website will be available at: http://localhost:$PORT"
    echo ""
    echo "📝 Press Ctrl+C to stop the server"
    echo "----------------------------------------"
    python3 -m http.server $PORT
elif command -v python &> /dev/null; then
    PORT=$(find_available_port)
    if [ $PORT -ne 8000 ]; then
        echo "⚠️  Port 8000 is in use, using port $PORT instead"
    fi
    echo "✅ Python found - Starting HTTP server on port $PORT"
    echo "🌐 Website will be available at: http://localhost:$PORT"
    echo ""
    echo "📝 Press Ctrl+C to stop the server"
    echo "----------------------------------------"
    python -m http.server $PORT
elif command -v php &> /dev/null; then
    PORT=$(find_available_port)
    if [ $PORT -ne 8000 ]; then
        echo "⚠️  Port 8000 is in use, using port $PORT instead"
    fi
    echo "✅ PHP found - Starting HTTP server on port $PORT"
    echo "🌐 Website will be available at: http://localhost:$PORT"
    echo ""
    echo "📝 Press Ctrl+C to stop the server"
    echo "----------------------------------------"
    php -S localhost:$PORT
elif command -v node &> /dev/null; then
    echo "✅ Node.js found - Attempting to use http-server"
    if command -v npx &> /dev/null; then
        PORT=$(find_available_port)
        if [ $PORT -ne 8000 ]; then
            echo "⚠️  Port 8000 is in use, using port $PORT instead"
        fi
        echo "🌐 Website will be available at: http://localhost:$PORT"
        echo ""
        echo "📝 Press Ctrl+C to stop the server"
        echo "----------------------------------------"
        npx http-server -p $PORT
    else
        echo "❌ npx not found. Please install Node.js with npm or use Python instead."
        exit 1
    fi
else
    echo "❌ No suitable web server found!"
    echo ""
    echo "Please install one of the following:"
    echo "  - Python 3: https://www.python.org/downloads/"
    echo "  - PHP: https://www.php.net/downloads"
    echo "  - Node.js: https://nodejs.org/en/download/"
    echo ""
    echo "Or simply open index.html directly in your web browser."
    exit 1
fi
