/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.top-bar {
    background: #f8f9fa;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.top-bar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.login-section h4 {
    margin-bottom: 10px;
    color: #007bff;
}

.login-form {
    display: flex;
    gap: 10px;
    align-items: center;
}

.login-form input {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.login-form button {
    padding: 5px 15px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.login-form button:hover {
    background: #0056b3;
}

.forgot-password {
    color: #007bff;
    text-decoration: none;
    font-size: 12px;
}

.contact-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.contact-info ul {
    display: flex;
    list-style: none;
    gap: 15px;
}

.contact-info a {
    text-decoration: none;
    color: #333;
}

.contact-details {
    display: flex;
    gap: 15px;
}

.contact-details a {
    color: #007bff;
    text-decoration: none;
}

/* Main Navigation */
.main-nav {
    padding: 15px 0;
}

.main-nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo img {
    height: 50px;
}

.logo-retina {
    display: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 10px 0;
}

.nav-menu a:hover {
    color: #007bff;
}

/* Dropdown Styles */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    list-style: none;
    min-width: 200px;
    display: none;
    z-index: 1000;
}

.dropdown:hover .dropdown-menu {
    display: block;
}

.dropdown-menu li {
    border-bottom: 1px solid #eee;
}

.dropdown-menu a {
    display: block;
    padding: 10px 15px;
    color: #333;
    text-decoration: none;
}

.dropdown-menu a:hover {
    background: #f8f9fa;
    color: #007bff;
}

/* Service Tabs */
.service-tabs {
    background: #f8f9fa;
    padding: 40px 0;
}

.tabs {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.tab-buttons {
    display: flex;
    background: #007bff;
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    background: transparent;
    border: none;
    color: white;
    cursor: pointer;
    font-weight: 500;
}

.tab-btn.active,
.tab-btn:hover {
    background: rgba(255,255,255,0.2);
}

.tab-content {
    padding: 30px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.schedule-form,
.tracking-form,
.dg-form {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.schedule-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.schedule-form select,
.tracking-form input,
.dg-form input {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-width: 200px;
}

.schedule-form button,
.tracking-form button,
.dg-form button {
    padding: 10px 20px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.schedule-form button:hover,
.tracking-form button:hover,
.dg-form button:hover {
    background: #0056b3;
}

/* Promotions */
.promotions {
    padding: 20px 0;
    background: #fff3cd;
    text-align: center;
}

.promotions h3 {
    color: #856404;
}

/* About Section */
.about-section {
    padding: 60px 0;
    background: white;
}

.about-section h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #007bff;
    font-size: 2.5em;
}

.about-section p {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.1em;
    line-height: 1.8;
}

/* Services Section */
.services-section {
    padding: 60px 0;
    background: #f8f9fa;
}

.services-section h3 {
    text-align: center;
    margin-bottom: 10px;
    color: #007bff;
    font-size: 2em;
}

.services-section > .container > p {
    text-align: center;
    margin-bottom: 50px;
    color: #666;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.service-item {
    background: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.service-item:hover {
    transform: translateY(-5px);
}

.service-item img {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
}

.service-item h4 {
    margin-bottom: 15px;
    color: #007bff;
    font-size: 1.3em;
}

.service-item p {
    color: #666;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 40px;
    align-items: center;
    margin-bottom: 30px;
}

.footer-logo img {
    height: 60px;
}

.footer-text p {
    line-height: 1.6;
    max-width: 600px;
}

.footer-contact {
    display: flex;
    gap: 30px;
}

.contact-item span {
    display: block;
    color: #ccc;
    margin-bottom: 5px;
}

.contact-item h3 {
    color: #007bff;
    margin: 0;
}

.footer-bottom {
    border-top: 1px solid #555;
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-links a {
    color: #ccc;
    text-decoration: none;
}

.footer-links a:hover {
    color: white;
}

/* Page Header Styles */
.page-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.page-header h1 {
    font-size: 3em;
    margin-bottom: 15px;
}

.page-header p {
    font-size: 1.2em;
    opacity: 0.9;
}

/* About Page Styles */
.company-overview {
    padding: 60px 0;
    background: white;
}

.overview-content h2 {
    color: #007bff;
    margin-bottom: 20px;
    font-size: 2.2em;
}

.overview-content h3 {
    color: #333;
    margin: 30px 0 15px;
    font-size: 1.5em;
}

.overview-content ul {
    list-style: none;
    padding-left: 0;
}

.overview-content li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.company-stats {
    padding: 60px 0;
    background: #f8f9fa;
}

.company-stats h2 {
    text-align: center;
    margin-bottom: 50px;
    color: #007bff;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
}

.stat-item {
    text-align: center;
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.stat-item h3 {
    font-size: 2.5em;
    color: #007bff;
    margin-bottom: 10px;
}

.why-choose-us {
    padding: 60px 0;
    background: white;
}

.why-choose-us h2 {
    text-align: center;
    margin-bottom: 50px;
    color: #007bff;
}

.reasons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.reason-item {
    padding: 20px;
    border-left: 4px solid #007bff;
    background: #f8f9fa;
}

.reason-item h4 {
    color: #007bff;
    margin-bottom: 10px;
}

/* Services Page Styles */
.services-overview {
    padding: 40px 0;
    background: white;
    text-align: center;
}

.services-overview h2 {
    color: #007bff;
    margin-bottom: 20px;
}

.detailed-services {
    padding: 60px 0;
    background: #f8f9fa;
}

.service-detail {
    margin-bottom: 60px;
}

.service-content {
    display: grid;
    grid-template-columns: 150px 1fr;
    gap: 40px;
    align-items: start;
    background: white;
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.service-content.reverse {
    grid-template-columns: 1fr 150px;
}

.service-content img {
    width: 120px;
    height: 120px;
}

.service-text h3 {
    color: #007bff;
    margin-bottom: 15px;
    font-size: 1.8em;
}

.service-text ul {
    margin-top: 15px;
    padding-left: 20px;
}

.service-text li {
    margin-bottom: 8px;
}

.service-features {
    padding: 60px 0;
    background: white;
}

.service-features h2 {
    text-align: center;
    margin-bottom: 50px;
    color: #007bff;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.feature-item {
    text-align: center;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 8px;
}

.feature-item h4 {
    color: #007bff;
    margin-bottom: 15px;
}

/* Contact Page Styles */
.contact-section {
    padding: 60px 0;
    background: white;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.contact-details-list {
    margin-top: 30px;
}

.contact-detail {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.contact-detail h4 {
    color: #007bff;
    margin-bottom: 10px;
}

.contact-form {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 8px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.submit-btn {
    background: #007bff;
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    width: 100%;
}

.submit-btn:hover {
    background: #0056b3;
}

.office-locations {
    padding: 60px 0;
    background: #f8f9fa;
}

.office-locations h2 {
    text-align: center;
    margin-bottom: 50px;
    color: #007bff;
}

.locations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.location-item {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.location-item h3 {
    color: #007bff;
    margin-bottom: 10px;
}

.location-details {
    margin-top: 20px;
}

.map-section {
    padding: 60px 0;
    background: white;
}

.map-section h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #007bff;
}

.map-placeholder {
    height: 400px;
    background: #f8f9fa;
    border: 2px dashed #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #666;
    border-radius: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .top-bar .container {
        flex-direction: column;
        gap: 15px;
    }

    .main-nav .container {
        flex-direction: column;
        gap: 20px;
    }

    .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 10px;
    }

    .schedule-form,
    .tracking-form,
    .dg-form {
        flex-direction: column;
        align-items: stretch;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .service-content,
    .service-content.reverse {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .page-header h1 {
        font-size: 2em;
    }
}

/* News Page Styles */
.news-section {
    padding: 60px 0;
    background: white;
}

.news-grid {
    display: grid;
    gap: 30px;
    margin-bottom: 50px;
}

.news-item {
    display: grid;
    grid-template-columns: 100px 1fr;
    gap: 20px;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.news-date {
    text-align: center;
    background: #007bff;
    color: white;
    padding: 15px;
    border-radius: 8px;
    height: fit-content;
}

.news-date .day {
    display: block;
    font-size: 2em;
    font-weight: bold;
}

.news-date .month {
    display: block;
    font-size: 0.9em;
    text-transform: uppercase;
}

.news-date .year {
    display: block;
    font-size: 0.8em;
    opacity: 0.8;
}

.news-content h3 {
    color: #007bff;
    margin-bottom: 15px;
    font-size: 1.4em;
}

.news-content p {
    line-height: 1.6;
    margin-bottom: 15px;
    color: #666;
}

.read-more {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
}

.read-more:hover {
    text-decoration: underline;
}

.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.page-btn {
    padding: 10px 15px;
    border: 1px solid #ddd;
    background: white;
    color: #333;
    cursor: pointer;
    border-radius: 4px;
}

.page-btn.active,
.page-btn:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.newsletter-section {
    padding: 60px 0;
    background: #007bff;
    color: white;
    text-align: center;
}

.newsletter-content h2 {
    margin-bottom: 15px;
    font-size: 2em;
}

.newsletter-content p {
    margin-bottom: 30px;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    max-width: 400px;
    margin: 0 auto;
    gap: 10px;
}

.newsletter-form input {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 4px;
}

.newsletter-form button {
    padding: 12px 20px;
    background: white;
    color: #007bff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.newsletter-form button:hover {
    background: #f8f9fa;
}

/* Promotions Page Styles */
.featured-promotion {
    padding: 40px 0;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    text-align: center;
}

.promotion-banner {
    background: rgba(255,255,255,0.1);
    padding: 40px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.promotion-banner h2 {
    font-size: 2.5em;
    margin-bottom: 15px;
}

.promotion-banner p {
    font-size: 1.2em;
    margin-bottom: 25px;
    opacity: 0.9;
}

.promotion-details {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.promo-code {
    background: rgba(255,255,255,0.2);
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: bold;
}

.validity {
    background: rgba(255,255,255,0.2);
    padding: 10px 20px;
    border-radius: 25px;
}

.cta-button {
    background: white;
    color: #28a745;
    padding: 15px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.cta-button:hover {
    transform: translateY(-2px);
}

.promotions-section {
    padding: 60px 0;
    background: white;
}

.promotions-section h2 {
    text-align: center;
    margin-bottom: 50px;
    color: #007bff;
    font-size: 2.2em;
}

.promotions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.promotion-item {
    background: #f8f9fa;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.promotion-item:hover {
    transform: translateY(-5px);
}

.promotion-header {
    background: #007bff;
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.promotion-header h3 {
    margin: 0;
    font-size: 1.3em;
}

.discount {
    background: #28a745;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9em;
}

.promotion-content {
    padding: 25px;
}

.promotion-content p {
    margin-bottom: 15px;
    color: #666;
}

.promotion-content ul {
    margin-bottom: 20px;
    padding-left: 20px;
}

.promotion-content li {
    margin-bottom: 8px;
    color: #555;
}

.promotion-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.promotion-footer .validity {
    font-size: 0.9em;
    color: #666;
    background: none;
    padding: 0;
}

.apply-btn {
    background: #007bff;
    color: white;
    padding: 8px 20px;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 500;
}

.apply-btn:hover {
    background: #0056b3;
}

.terms-section {
    padding: 40px 0;
    background: #f8f9fa;
}

.terms-section h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #007bff;
}

.terms-content ul {
    max-width: 800px;
    margin: 0 auto;
    padding-left: 20px;
}

.terms-content li {
    margin-bottom: 10px;
    color: #666;
}

.promotion-contact {
    padding: 60px 0;
    background: #007bff;
    color: white;
    text-align: center;
}

.contact-promotion h2 {
    margin-bottom: 15px;
    font-size: 2.2em;
}

.contact-promotion p {
    margin-bottom: 30px;
    opacity: 0.9;
    font-size: 1.1em;
}

.contact-options {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.contact-btn {
    background: white;
    color: #007bff;
    padding: 15px 25px;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    transition: transform 0.3s ease;
}

.contact-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Additional Responsive Styles */
@media (max-width: 768px) {
    .news-item {
        grid-template-columns: 80px 1fr;
        gap: 15px;
        padding: 20px;
    }

    .newsletter-form {
        flex-direction: column;
    }

    .promotion-details {
        flex-direction: column;
        gap: 15px;
    }

    .promotion-footer {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .contact-options {
        flex-direction: column;
        align-items: center;
    }

    .contact-btn {
        width: 100%;
        max-width: 300px;
    }
}
