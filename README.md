# CMS Logistics Group - Static Website Copy

This is a static copy of the CMS Logistics Group website (https://www.cmslogistics.com) created for local deployment and development purposes.

## Project Overview

This project contains a complete static version of the CMS Logistics website with the following features:

### ✅ Completed Features
- **Static Content**: All static content has been preserved and converted to HTML
- **Responsive Design**: Mobile-friendly layout with modern CSS
- **Navigation**: Complete navigation structure with all pages
- **Visual Assets**: All images and icons downloaded and integrated
- **Page Structure**: 
  - Home page (index.html)
  - About Us (about.html)
  - Services (services.html)
  - News (news.html)
  - Promotions (promotions.html)
  - Contact (contact.html)

### ⚠️ Modified/Disabled Features
- **Login System**: Disabled (shows alert message)
- **Database Queries**: All dynamic queries disabled with user-friendly messages
- **Booking System**: Converted to contact form redirects
- **Tracking System**: Disabled with informational messages
- **Schedule Queries**: Static display with database connection alerts

### 🔧 Technical Details
- **Technology**: Pure HTML, CSS, and JavaScript (no server-side dependencies)
- **Framework**: Vanilla JavaScript with responsive CSS Grid/Flexbox
- **Images**: All original images downloaded and stored locally
- **Compatibility**: Works with any modern web browser
- **No Dependencies**: No external libraries or frameworks required

## File Structure

```
webpage-cms/
├── index.html              # Main homepage
├── about.html              # About Us page
├── services.html           # Services page
├── news.html               # News page
├── promotions.html         # Promotions page
├── contact.html            # Contact page
├── css/
│   └── style.css           # Main stylesheet
├── js/
│   └── script.js           # JavaScript functionality
├── images/
│   ├── logo.png            # Company logo
│   └── <EMAIL>         # High-res logo
├── cms/
│   ├── s1.png              # Service icon 1
│   ├── s2.png              # Service icon 2
│   ├── s3.png              # Service icon 3
│   ├── s4.png              # Service icon 4
│   ├── s5.png              # Service icon 5
│   └── s6.png              # Service icon 6
└── README.md               # This file
```

## How to Run

### Option 1: Simple File Opening
1. Open `index.html` in any modern web browser
2. Navigate through the site using the menu

### Option 2: Local Web Server (Recommended)
1. Using Python:
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Python 2
   python -m SimpleHTTPServer 8000
   ```

2. Using Node.js (if you have it installed):
   ```bash
   npx http-server
   ```

3. Using PHP (if you have it installed):
   ```bash
   php -S localhost:8000
   ```

4. Open your browser and go to `http://localhost:8000`

## Features and Functionality

### Working Features
- ✅ Responsive navigation menu
- ✅ Service tabs on homepage
- ✅ Contact form (shows alert - can be connected to email service)
- ✅ Newsletter signup (shows alert - can be connected to email service)
- ✅ All static content display
- ✅ Image galleries and service descriptions
- ✅ Mobile-responsive design

### Disabled Features (Database Required)
- ❌ User login system
- ❌ Sailing schedule queries
- ❌ Online booking system
- ❌ Shipment tracking
- ❌ Container status checking
- ❌ DG approval status
- ❌ Real-time data queries

## Customization

### To Enable Dynamic Features
1. Set up a backend server (Node.js, PHP, Python, etc.)
2. Create database connections for:
   - User authentication
   - Shipping schedules
   - Booking system
   - Tracking system
3. Replace alert messages with actual API calls
4. Update form submissions to send data to your backend

### To Modify Content
1. Edit HTML files directly for content changes
2. Modify `css/style.css` for styling changes
3. Update `js/script.js` for functionality changes

### To Add New Pages
1. Create new HTML file following the existing template structure
2. Add navigation links in all existing pages
3. Update the main navigation menu

## Contact Information

**Original Website**: https://www.cmslogistics.com

**Company Contact**:
- Phone: +65 6238 5115
- Email: <EMAIL>

## Notes

- This is a static copy created for development purposes
- All dynamic functionality has been disabled and replaced with informational messages
- The original website's database-dependent features would need to be reimplemented with a backend server
- All images and content belong to CMS Logistics Group
- This copy maintains the original design and layout while providing a foundation for further development

## Next Steps for Development

1. **Backend Integration**: Set up server-side technology (Node.js, PHP, Python, etc.)
2. **Database Setup**: Create databases for users, schedules, bookings, etc.
3. **API Development**: Build APIs for dynamic functionality
4. **Authentication**: Implement user login and session management
5. **Real-time Data**: Connect to shipping and logistics data sources
6. **Form Processing**: Set up email and data processing for forms
7. **Content Management**: Consider adding a CMS for easy content updates

This static version provides a solid foundation for rebuilding the dynamic functionality while preserving the original design and user experience.
